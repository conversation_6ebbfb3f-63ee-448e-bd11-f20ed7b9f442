package com.example.modid;

import net.minecraft.client.util.ITooltipFlag;
import net.minecraft.creativetab.CreativeTabs;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ItemSword;
import net.minecraft.util.ActionResult;
import net.minecraft.util.EnumActionResult;
import net.minecraft.util.EnumHand;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import javax.annotation.Nullable;
import java.util.List;

public class TianshiSword extends ItemSword {
    
    public TianshiSword() {
        super(ToolMaterial.DIAMOND);
        setCreativeTab(CreativeTabs.COMBAT);
        setMaxDamage(0); // 无限耐久
    }
    
    @Override
    @SideOnly(Side.CLIENT)
    public void addInformation(ItemStack stack, @Nullable World worldIn, List<String> tooltip, ITooltipFlag flagIn) {
        // 创建彩色闪烁的tooltip文字
        String text = "秒天秒地秒空气无敌神剑，手持右键秒杀全图";
        
        // 使用不同颜色创建闪烁效果
        TextFormatting[] colors = {
            TextFormatting.RED, TextFormatting.GOLD, TextFormatting.YELLOW, 
            TextFormatting.GREEN, TextFormatting.AQUA, TextFormatting.BLUE, 
            TextFormatting.LIGHT_PURPLE, TextFormatting.WHITE
        };
        
        // 根据系统时间选择颜色，创建闪烁效果
        int colorIndex = (int) ((System.currentTimeMillis() / 200) % colors.length);
        TextFormatting currentColor = colors[colorIndex];
        
        // 添加格式化的tooltip
        tooltip.add(currentColor + "" + TextFormatting.BOLD + "" + TextFormatting.ITALIC + text);
        
        super.addInformation(stack, worldIn, tooltip, flagIn);
    }
    
    @Override
    public ActionResult<ItemStack> onItemRightClick(World worldIn, EntityPlayer playerIn, EnumHand handIn) {
        if (!worldIn.isRemote) {
            // 右键秒杀全图生物
            killAllEntities(worldIn, playerIn);
            playerIn.sendMessage(new TextComponentString(TextFormatting.RED + "天屎剑发动！全图生物已被秒杀！"));
        }
        return new ActionResult<>(EnumActionResult.SUCCESS, playerIn.getHeldItem(handIn));
    }
    
    private void killAllEntities(World world, EntityPlayer player) {
        // 获取世界中所有实体
        for (Entity entity : world.loadedEntityList) {
            // 排除玩家自己，只杀死其他生物
            if (entity instanceof EntityLivingBase && entity != player) {
                EntityLivingBase livingEntity = (EntityLivingBase) entity;
                // 直接设置生命值为0来秒杀
                livingEntity.setHealth(0.0F);
                livingEntity.onDeath(null);
            }
        }
    }
    
    @Override
    public boolean isDamageable() {
        return false; // 不会损坏
    }
    
    @Override
    public boolean getIsRepairable(ItemStack toRepair, ItemStack repair) {
        return false; // 不需要修复
    }
    
    @Override
    public boolean hasEffect(ItemStack stack) {
        return true; // 始终有附魔光效
    }
}
