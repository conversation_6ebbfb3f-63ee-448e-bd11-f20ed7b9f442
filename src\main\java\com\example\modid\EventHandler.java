package com.example.modid;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.fml.common.eventhandler.EventPriority;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

public class EventHandler {

    // 拦截攻击事件（最早的伤害事件）
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerAttack(LivingAttackEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (isHoldingTianshiSword(player)) {
                event.setCanceled(true);
                // 可选：显示无敌提示
                 
                player.sendMessage(new TextComponentString(TextFormatting.GOLD + "天屎剑护体！无敌状态！"));
                
            }
        }
    }

    // 拦截伤害计算事件
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerHurt(LivingHurtEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (isHoldingTianshiSword(player)) {
                event.setCanceled(true);
            }
        }
    }

    // 拦截最终伤害事件
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerDamage(LivingDamageEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (isHoldingTianshiSword(player)) {
                event.setCanceled(true);
            }
        }
    }

    private boolean isHoldingTianshiSword(EntityPlayer player) {
        // 检查主手
        ItemStack mainHand = player.getHeldItemMainhand();
        if (!mainHand.isEmpty() && mainHand.getItem() instanceof TianshiSword) {
            return true;
        }

        // 检查副手
        ItemStack offHand = player.getHeldItemOffhand();
        if (!offHand.isEmpty() && offHand.getItem() instanceof TianshiSword) {
            return true;
        }

        return false;
    }
}
