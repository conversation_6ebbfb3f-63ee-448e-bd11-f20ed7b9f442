package com.example.modid;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

public class EventHandler {
    
    @SubscribeEvent
    public void onPlayerHurt(LivingHurtEvent event) {
        // 检查受伤的实体是否为玩家
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            
            // 检查玩家是否持有天屎剑
            if (isHoldingTianshiSword(player)) {
                // 取消伤害事件，使玩家无敌
                event.setCanceled(true);
            }
        }
    }
    
    private boolean isHoldingTianshiSword(EntityPlayer player) {
        // 检查主手
        ItemStack mainHand = player.getHeldItemMainhand();
        if (!mainHand.isEmpty() && mainHand.getItem() instanceof TianshiSword) {
            return true;
        }
        
        // 检查副手
        ItemStack offHand = player.getHeldItemOffhand();
        if (!offHand.isEmpty() && offHand.getItem() instanceof TianshiSword) {
            return true;
        }
        
        return false;
    }
}
